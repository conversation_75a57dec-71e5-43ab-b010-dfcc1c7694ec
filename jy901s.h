/*
 * JY901S九轴传感器驱动头文件
 * 支持UART通信协议解析
 */

#ifndef JY901S_H
#define JY901S_H

#include "system_config.h"

/* JY901S数据帧结构 */
typedef struct {
    uint8_t header;             // 帧头 0x55
    uint8_t type;               // 数据类型
    uint8_t data[8];            // 数据载荷
    uint8_t checksum;           // 校验和
} JY901S_Frame_t;

/* JY901S接收状态 */
typedef enum {
    JY901S_STATE_WAIT_HEADER = 0,
    JY901S_STATE_WAIT_TYPE,
    JY901S_STATE_WAIT_DATA,
    JY901S_STATE_WAIT_CHECKSUM,
    JY901S_STATE_FRAME_READY
} JY901S_RxState_t;

/* JY901S接收缓冲区 */
typedef struct {
    uint8_t buffer[JY901S_RX_BUFFER_SIZE];
    uint16_t head;
    uint16_t tail;
    uint16_t count;
} JY901S_RxBuffer_t;

/* 函数声明 */

/**
 * @brief UART初始化
 */
void UART_Init(void);

/**
 * @brief JY901S初始化
 */
void JY901S_Init(void);

/**
 * @brief JY901S UART中断处理
 */
void JY901S_UART_IRQHandler(void);

/**
 * @brief 解析JY901S数据帧
 * @param frame 数据帧指针
 * @return true 解析成功, false 解析失败
 */
bool JY901S_ParseFrame(const JY901S_Frame_t *frame);

/**
 * @brief 获取加速度数据
 * @param accel 加速度数据指针
 * @return true 数据有效, false 数据无效
 */
bool JY901S_GetAccel(Vector3f_t *accel);

/**
 * @brief 获取角速度数据
 * @param gyro 角速度数据指针
 * @return true 数据有效, false 数据无效
 */
bool JY901S_GetGyro(Vector3f_t *gyro);

/**
 * @brief 获取角度数据
 * @param angle 角度数据指针
 * @return true 数据有效, false 数据无效
 */
bool JY901S_GetAngle(Vector3f_t *angle);

/**
 * @brief 获取磁场数据
 * @param mag 磁场数据指针
 * @return true 数据有效, false 数据无效
 */
bool JY901S_GetMag(Vector3f_t *mag);

/**
 * @brief 获取温度数据
 * @return 温度值(°C)
 */
float JY901S_GetTemperature(void);

/**
 * @brief 检查传感器健康状态
 * @return true 健康, false 异常
 */
bool JY901S_IsHealthy(void);

/**
 * @brief 获取数据更新标志
 * @return true 有新数据, false 无新数据
 */
bool JY901S_IsDataReady(void);

/**
 * @brief 清除数据更新标志
 */
void JY901S_ClearDataReady(void);

/**
 * @brief 计算校验和
 * @param data 数据指针
 * @param len 数据长度
 * @return 校验和
 */
uint8_t JY901S_CalcChecksum(const uint8_t *data, uint8_t len);

/**
 * @brief 重置接收状态机
 */
void JY901S_ResetRxState(void);

/**
 * @brief 获取错误计数
 * @return 错误计数
 */
uint32_t JY901S_GetErrorCount(void);

#endif /* JY901S_H */
