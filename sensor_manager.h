/*
 * 传感器数据管理模块头文件
 */

#ifndef SENSOR_MANAGER_H
#define SENSOR_MANAGER_H

#include "system_config.h"
#include "jy901s.h"

/* 数据滤波参数 */
#define FILTER_ALPHA                0.8f    // 低通滤波系数
#define DATA_VALID_TIMEOUT          200     // 数据有效超时时间(ms)

/* 传感器管理器状态 */
typedef enum {
    SENSOR_STATE_INIT = 0,
    SENSOR_STATE_RUNNING,
    SENSOR_STATE_ERROR,
    SENSOR_STATE_TIMEOUT
} SensorState_t;

/* 函数声明 */

/**
 * @brief 传感器管理器初始化
 */
void SensorManager_Init(void);

/**
 * @brief 传感器数据更新
 * 应在100Hz频率下调用
 */
void SensorManager_Update(void);

/**
 * @brief 获取欧拉角数据
 * @param angle 角度数据指针
 * @return true 数据有效, false 数据无效
 */
bool SensorManager_GetEulerAngles(Vector3f_t *angle);

/**
 * @brief 获取加速度数据
 * @param accel 加速度数据指针
 * @return true 数据有效, false 数据无效
 */
bool SensorManager_GetAcceleration(Vector3f_t *accel);

/**
 * @brief 获取角速度数据
 * @param gyro 角速度数据指针
 * @return true 数据有效, false 数据无效
 */
bool SensorManager_GetAngularVelocity(Vector3f_t *gyro);

/**
 * @brief 获取磁场数据
 * @param mag 磁场数据指针
 * @return true 数据有效, false 数据无效
 */
bool SensorManager_GetMagnetic(Vector3f_t *mag);

/**
 * @brief 获取温度数据
 * @return 温度值(°C)
 */
float SensorManager_GetTemperature(void);

/**
 * @brief 获取传感器状态
 * @return 传感器状态
 */
SensorState_t SensorManager_GetState(void);

/**
 * @brief 检查数据是否有效
 * @return true 数据有效, false 数据无效
 */
bool SensorManager_IsDataValid(void);

/**
 * @brief 获取数据更新频率
 * @return 更新频率(Hz)
 */
float SensorManager_GetUpdateRate(void);

/**
 * @brief 重置传感器管理器
 */
void SensorManager_Reset(void);

/**
 * @brief 获取错误计数
 * @return 错误计数
 */
uint32_t SensorManager_GetErrorCount(void);

#endif /* SENSOR_MANAGER_H */
