/*
 * OLED显示管理模块头文件
 */

#ifndef DISPLAY_MANAGER_H
#define DISPLAY_MANAGER_H

#include "system_config.h"
#include "ssd1306.h"
#include "sensor_manager.h"

/* 显示配置 */
#define DISPLAY_AUTO_SWITCH_TIME    3000    // 自动切换时间(ms)
#define DISPLAY_REFRESH_RATE        20      // 显示刷新率(Hz)

/* 显示模式 */
typedef enum {
    DISPLAY_MODE_AUTO = 0,      // 自动切换
    DISPLAY_MODE_MANUAL         // 手动切换
} DisplayMode_t;

/* 函数声明 */

/**
 * @brief 显示管理器初始化
 */
void DisplayManager_Init(void);

/**
 * @brief 显示管理器更新
 * 应在20Hz频率下调用
 */
void DisplayManager_Update(void);

/**
 * @brief 切换到下一页
 */
void DisplayManager_NextPage(void);

/**
 * @brief 切换到上一页
 */
void DisplayManager_PrevPage(void);

/**
 * @brief 设置显示页面
 * @param page 页面编号
 */
void DisplayManager_SetPage(DisplayPage_t page);

/**
 * @brief 获取当前页面
 * @return 当前页面编号
 */
DisplayPage_t DisplayManager_GetCurrentPage(void);

/**
 * @brief 设置显示模式
 * @param mode 显示模式
 */
void DisplayManager_SetMode(DisplayMode_t mode);

/**
 * @brief 获取显示模式
 * @return 显示模式
 */
DisplayMode_t DisplayManager_GetMode(void);

/**
 * @brief 显示启动画面
 */
void DisplayManager_ShowSplash(void);

/**
 * @brief 显示错误信息
 * @param error_msg 错误信息
 */
void DisplayManager_ShowError(const char *error_msg);

/**
 * @brief 显示状态信息
 */
void DisplayManager_ShowStatus(void);

/**
 * @brief 强制刷新显示
 */
void DisplayManager_ForceRefresh(void);

/**
 * @brief 检查显示是否正常
 * @return true 正常, false 异常
 */
bool DisplayManager_IsHealthy(void);

#endif /* DISPLAY_MANAGER_H */
