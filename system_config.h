/*
 * 系统配置文件
 * MSPM0G3507 + JY901S + OLED 显示系统
 */

#ifndef SYSTEM_CONFIG_H
#define SYSTEM_CONFIG_H

#include "ti_msp_dl_config.h"
#include <stdint.h>
#include <stdbool.h>

/* 系统时钟配置 */
#define CPUCLK_FREQ                 80000000    // 80MHz主频
#define SYSTEM_TICK_FREQ            1000        // 1ms系统时钟

/* UART配置 - JY901S通信 */
#define JY901S_UART_INST            UART_0_INST
#define JY901S_UART_BAUDRATE        9600
#define JY901S_UART_RX_PIN          IOMUX_PINCM22  // PA11
#define JY901S_UART_TX_PIN          IOMUX_PINCM21  // PA10

/* I2C配置 - OLED通信 */
#define OLED_I2C_INST               I2C_0_INST
#define OLED_I2C_SPEED              400000      // 400kHz
#define OLED_I2C_SDA_PIN            IOMUX_PINCM1   // PA0
#define OLED_I2C_SCL_PIN            IOMUX_PINCM2   // PA1
#define OLED_I2C_ADDRESS            0x3C

/* GPIO配置 */
#define GPIO_LEDS_PORT              GPIOA
#define GPIO_LEDS_USER_LED_1_PIN    DL_GPIO_PIN_26  // PA26

/* 任务调度配置 */
#define TASK_SENSOR_FREQ            100         // 100Hz传感器任务
#define TASK_DISPLAY_FREQ           20          // 20Hz显示任务  
#define TASK_STATUS_FREQ            1           // 1Hz状态任务

/* JY901S协议配置 */
#define JY901S_FRAME_HEADER         0x55
#define JY901S_FRAME_SIZE           11
#define JY901S_RX_BUFFER_SIZE       64

/* JY901S数据类型 */
#define JY901S_TYPE_ACCEL           0x51        // 加速度
#define JY901S_TYPE_GYRO            0x52        // 角速度
#define JY901S_TYPE_ANGLE           0x53        // 角度
#define JY901S_TYPE_MAG             0x54        // 磁场

/* OLED显示配置 */
#define OLED_WIDTH                  128
#define OLED_HEIGHT                 64
#define OLED_PAGES                  8
#define OLED_BUFFER_SIZE            (OLED_WIDTH * OLED_HEIGHT / 8)

/* 显示页面定义 */
typedef enum {
    DISPLAY_PAGE_ANGLE = 0,     // 角度页面
    DISPLAY_PAGE_ACCEL,         // 加速度页面
    DISPLAY_PAGE_GYRO,          // 角速度页面
    DISPLAY_PAGE_MAX
} DisplayPage_t;

/* 传感器数据结构 */
typedef struct {
    float x;
    float y; 
    float z;
} Vector3f_t;

typedef struct {
    Vector3f_t accel;           // 加速度 (g)
    Vector3f_t gyro;            // 角速度 (°/s)
    Vector3f_t angle;           // 角度 (°)
    Vector3f_t mag;             // 磁场 (mG)
    float temperature;          // 温度 (°C)
    uint32_t timestamp;         // 时间戳
    bool data_valid;            // 数据有效标志
} SensorData_t;

/* 系统状态结构 */
typedef struct {
    bool sensor_online;         // 传感器在线状态
    bool display_online;        // 显示屏在线状态
    uint32_t sensor_error_count; // 传感器错误计数
    uint32_t display_error_count; // 显示错误计数
    DisplayPage_t current_page;   // 当前显示页面
} SystemStatus_t;

/* 全局变量声明 */
extern SensorData_t g_sensor_data;
extern SystemStatus_t g_system_status;

/* 工具宏定义 */
#define ARRAY_SIZE(arr)             (sizeof(arr) / sizeof((arr)[0]))
#define MIN(a, b)                   ((a) < (b) ? (a) : (b))
#define MAX(a, b)                   ((a) > (b) ? (a) : (b))
#define CLAMP(val, min, max)        (MIN(MAX(val, min), max))

/* 数学常量 */
#define PI                          3.14159265f
#define DEG_TO_RAD                  (PI / 180.0f)
#define RAD_TO_DEG                  (180.0f / PI)

/* 调试配置 */
#ifdef DEBUG
    #define DEBUG_PRINT(fmt, ...)   printf(fmt, ##__VA_ARGS__)
#else
    #define DEBUG_PRINT(fmt, ...)   
#endif

/* 错误代码定义 */
typedef enum {
    ERROR_NONE = 0,
    ERROR_UART_INIT,
    ERROR_I2C_INIT,
    ERROR_SENSOR_INIT,
    ERROR_DISPLAY_INIT,
    ERROR_DATA_INVALID,
    ERROR_COMMUNICATION
} ErrorCode_t;

#endif /* SYSTEM_CONFIG_H */
