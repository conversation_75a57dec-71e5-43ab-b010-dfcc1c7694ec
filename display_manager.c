/*
 * OLED显示管理模块实现
 */

#include "display_manager.h"
#include <stdio.h>
#include <string.h>

/* 静态变量 */
static DisplayPage_t current_page = DISPLAY_PAGE_ANGLE;
static DisplayMode_t display_mode = DISPLAY_MODE_AUTO;
static uint32_t last_switch_time = 0;
static uint32_t last_refresh_time = 0;
static bool display_healthy = false;

/* 页面显示函数 */
static void DisplayPage_Angle(void);
static void DisplayPage_Accel(void);
static void DisplayPage_Gyro(void);
static void DrawPageIndicator(void);

/**
 * @brief 显示管理器初始化
 */
void DisplayManager_Init(void)
{
    current_page = DISPLAY_PAGE_ANGLE;
    display_mode = DISPLAY_MODE_AUTO;
    last_switch_time = g_system_tick;
    last_refresh_time = g_system_tick;
    
    /* 检查OLED是否在线 */
    display_healthy = SSD1306_IsOnline();
    
    if (display_healthy)
    {
        /* 显示启动画面 */
        DisplayManager_ShowSplash();
    }
}

/**
 * @brief 显示管理器更新
 */
void DisplayManager_Update(void)
{
    uint32_t current_time = g_system_tick;
    
    /* 检查刷新频率 */
    if ((current_time - last_refresh_time) < (1000 / DISPLAY_REFRESH_RATE))
    {
        return;
    }
    last_refresh_time = current_time;
    
    /* 自动切换页面 */
    if (display_mode == DISPLAY_MODE_AUTO)
    {
        if ((current_time - last_switch_time) >= DISPLAY_AUTO_SWITCH_TIME)
        {
            DisplayManager_NextPage();
            last_switch_time = current_time;
        }
    }
    
    /* 清屏 */
    SSD1306_Clear();
    
    /* 根据当前页面显示内容 */
    switch (current_page)
    {
        case DISPLAY_PAGE_ANGLE:
            DisplayPage_Angle();
            break;
        case DISPLAY_PAGE_ACCEL:
            DisplayPage_Accel();
            break;
        case DISPLAY_PAGE_GYRO:
            DisplayPage_Gyro();
            break;
        default:
            break;
    }
    
    /* 绘制页面指示器 */
    DrawPageIndicator();
    
    /* 更新显示 */
    SSD1306_Update();
}

/**
 * @brief 显示角度页面
 */
static void DisplayPage_Angle(void)
{
    Vector3f_t angle;
    
    /* 显示标题 */
    SSD1306_ShowString(32, 0, "姿态角度", FONT_SIZE_12);
    
    if (SensorManager_GetEulerAngles(&angle))
    {
        /* 显示Roll角 */
        SSD1306_ShowString(0, 16, "横滚:", FONT_SIZE_8);
        SSD1306_ShowFloat(40, 16, angle.x, 1, FONT_SIZE_8);
        SSD1306_ShowString(90, 16, "°", FONT_SIZE_8);
        
        /* 显示Pitch角 */
        SSD1306_ShowString(0, 28, "俯仰:", FONT_SIZE_8);
        SSD1306_ShowFloat(40, 28, angle.y, 1, FONT_SIZE_8);
        SSD1306_ShowString(90, 28, "°", FONT_SIZE_8);
        
        /* 显示Yaw角 */
        SSD1306_ShowString(0, 40, "偏航:", FONT_SIZE_8);
        SSD1306_ShowFloat(40, 40, angle.z, 1, FONT_SIZE_8);
        SSD1306_ShowString(90, 40, "°", FONT_SIZE_8);
    }
    else
    {
        SSD1306_ShowString(20, 30, "数据无效", FONT_SIZE_12);
    }
}

/**
 * @brief 显示加速度页面
 */
static void DisplayPage_Accel(void)
{
    Vector3f_t accel;
    
    /* 显示标题 */
    SSD1306_ShowString(32, 0, "加速度", FONT_SIZE_12);
    
    if (SensorManager_GetAcceleration(&accel))
    {
        /* 显示X轴加速度 */
        SSD1306_ShowString(0, 16, "X轴:", FONT_SIZE_8);
        SSD1306_ShowFloat(32, 16, accel.x, 2, FONT_SIZE_8);
        SSD1306_ShowString(90, 16, "g", FONT_SIZE_8);
        
        /* 显示Y轴加速度 */
        SSD1306_ShowString(0, 28, "Y轴:", FONT_SIZE_8);
        SSD1306_ShowFloat(32, 28, accel.y, 2, FONT_SIZE_8);
        SSD1306_ShowString(90, 28, "g", FONT_SIZE_8);
        
        /* 显示Z轴加速度 */
        SSD1306_ShowString(0, 40, "Z轴:", FONT_SIZE_8);
        SSD1306_ShowFloat(32, 40, accel.z, 2, FONT_SIZE_8);
        SSD1306_ShowString(90, 40, "g", FONT_SIZE_8);
    }
    else
    {
        SSD1306_ShowString(20, 30, "数据无效", FONT_SIZE_12);
    }
}

/**
 * @brief 显示角速度页面
 */
static void DisplayPage_Gyro(void)
{
    Vector3f_t gyro;
    
    /* 显示标题 */
    SSD1306_ShowString(32, 0, "角速度", FONT_SIZE_12);
    
    if (SensorManager_GetAngularVelocity(&gyro))
    {
        /* 显示X轴角速度 */
        SSD1306_ShowString(0, 16, "X轴:", FONT_SIZE_8);
        SSD1306_ShowFloat(32, 16, gyro.x, 1, FONT_SIZE_8);
        SSD1306_ShowString(85, 16, "°/s", FONT_SIZE_8);
        
        /* 显示Y轴角速度 */
        SSD1306_ShowString(0, 28, "Y轴:", FONT_SIZE_8);
        SSD1306_ShowFloat(32, 28, gyro.y, 1, FONT_SIZE_8);
        SSD1306_ShowString(85, 28, "°/s", FONT_SIZE_8);
        
        /* 显示Z轴角速度 */
        SSD1306_ShowString(0, 40, "Z轴:", FONT_SIZE_8);
        SSD1306_ShowFloat(32, 40, gyro.z, 1, FONT_SIZE_8);
        SSD1306_ShowString(85, 40, "°/s", FONT_SIZE_8);
    }
    else
    {
        SSD1306_ShowString(20, 30, "数据无效", FONT_SIZE_12);
    }
}

/**
 * @brief 绘制页面指示器
 */
static void DrawPageIndicator(void)
{
    /* 在屏幕底部显示页面指示器 */
    for (int i = 0; i < DISPLAY_PAGE_MAX; i++)
    {
        uint8_t x = 50 + i * 12;
        uint8_t y = 56;
        
        if (i == current_page)
        {
            /* 当前页面用实心圆表示 */
            SSD1306_SetPixel(x, y, 1);
            SSD1306_SetPixel(x+1, y, 1);
            SSD1306_SetPixel(x, y+1, 1);
            SSD1306_SetPixel(x+1, y+1, 1);
        }
        else
        {
            /* 其他页面用空心圆表示 */
            SSD1306_SetPixel(x, y, 1);
            SSD1306_SetPixel(x+1, y+1, 1);
        }
    }
}

/**
 * @brief 切换到下一页
 */
void DisplayManager_NextPage(void)
{
    current_page = (DisplayPage_t)((current_page + 1) % DISPLAY_PAGE_MAX);
}

/**
 * @brief 切换到上一页
 */
void DisplayManager_PrevPage(void)
{
    current_page = (DisplayPage_t)((current_page + DISPLAY_PAGE_MAX - 1) % DISPLAY_PAGE_MAX);
}

/**
 * @brief 设置显示页面
 */
void DisplayManager_SetPage(DisplayPage_t page)
{
    if (page < DISPLAY_PAGE_MAX)
    {
        current_page = page;
    }
}

/**
 * @brief 获取当前页面
 */
DisplayPage_t DisplayManager_GetCurrentPage(void)
{
    return current_page;
}

/**
 * @brief 设置显示模式
 */
void DisplayManager_SetMode(DisplayMode_t mode)
{
    display_mode = mode;
    last_switch_time = g_system_tick;
}

/**
 * @brief 获取显示模式
 */
DisplayMode_t DisplayManager_GetMode(void)
{
    return display_mode;
}

/**
 * @brief 显示启动画面
 */
void DisplayManager_ShowSplash(void)
{
    SSD1306_Clear();
    SSD1306_ShowString(20, 10, "JY901S系统", FONT_SIZE_12);
    SSD1306_ShowString(30, 30, "初始化中...", FONT_SIZE_8);
    SSD1306_Update();
}

/**
 * @brief 显示错误信息
 */
void DisplayManager_ShowError(const char *error_msg)
{
    SSD1306_Clear();
    SSD1306_ShowString(30, 10, "系统错误", FONT_SIZE_12);
    SSD1306_ShowString(10, 30, error_msg, FONT_SIZE_8);
    SSD1306_Update();
}

/**
 * @brief 强制刷新显示
 */
void DisplayManager_ForceRefresh(void)
{
    last_refresh_time = 0;
}

/**
 * @brief 检查显示是否正常
 */
bool DisplayManager_IsHealthy(void)
{
    return display_healthy;
}
