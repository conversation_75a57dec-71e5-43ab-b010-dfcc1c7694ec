/*
 * SSD1306 OLED显示驱动头文件
 * 支持I2C通信和中文显示
 */

#ifndef SSD1306_H
#define SSD1306_H

#include "system_config.h"

/* SSD1306命令定义 */
#define SSD1306_CMD_SET_CONTRAST            0x81
#define SSD1306_CMD_DISPLAY_ALL_ON_RESUME   0xA4
#define SSD1306_CMD_DISPLAY_ALL_ON          0xA5
#define SSD1306_CMD_NORMAL_DISPLAY          0xA6
#define SSD1306_CMD_INVERT_DISPLAY          0xA7
#define SSD1306_CMD_DISPLAY_OFF             0xAE
#define SSD1306_CMD_DISPLAY_ON              0xAF
#define SSD1306_CMD_SET_DISPLAY_OFFSET      0xD3
#define SSD1306_CMD_SET_COM_PINS            0xDA
#define SSD1306_CMD_SET_VCOM_DETECT         0xDB
#define SSD1306_CMD_SET_DISPLAY_CLOCK_DIV   0xD5
#define SSD1306_CMD_SET_PRECHARGE           0xD9
#define SSD1306_CMD_SET_MULTIPLEX           0xA8
#define SSD1306_CMD_SET_LOW_COLUMN          0x00
#define SSD1306_CMD_SET_HIGH_COLUMN         0x10
#define SSD1306_CMD_SET_START_LINE          0x40
#define SSD1306_CMD_MEMORY_MODE             0x20
#define SSD1306_CMD_COLUMN_ADDR             0x21
#define SSD1306_CMD_PAGE_ADDR               0x22
#define SSD1306_CMD_COM_SCAN_INC            0xC0
#define SSD1306_CMD_COM_SCAN_DEC            0xC8
#define SSD1306_CMD_SEG_REMAP               0xA0
#define SSD1306_CMD_CHARGE_PUMP             0x8D
#define SSD1306_CMD_EXTERNAL_VCC           0x01
#define SSD1306_CMD_SWITCH_CAP_VCC         0x02

/* 字体大小定义 */
typedef enum {
    FONT_SIZE_8 = 8,
    FONT_SIZE_12 = 12,
    FONT_SIZE_16 = 16
} FontSize_t;

/* 函数声明 */

/**
 * @brief I2C初始化
 */
void I2C_Init(void);

/**
 * @brief SSD1306初始化
 */
void SSD1306_Init(void);

/**
 * @brief 写命令到SSD1306
 * @param cmd 命令字节
 */
void SSD1306_WriteCommand(uint8_t cmd);

/**
 * @brief 写数据到SSD1306
 * @param data 数据字节
 */
void SSD1306_WriteData(uint8_t data);

/**
 * @brief 清屏
 */
void SSD1306_Clear(void);

/**
 * @brief 更新显示
 */
void SSD1306_Update(void);

/**
 * @brief 设置像素点
 * @param x X坐标
 * @param y Y坐标
 * @param color 颜色(0或1)
 */
void SSD1306_SetPixel(uint8_t x, uint8_t y, uint8_t color);

/**
 * @brief 显示字符
 * @param x X坐标
 * @param y Y坐标
 * @param ch 字符
 * @param size 字体大小
 */
void SSD1306_ShowChar(uint8_t x, uint8_t y, char ch, FontSize_t size);

/**
 * @brief 显示字符串
 * @param x X坐标
 * @param y Y坐标
 * @param str 字符串
 * @param size 字体大小
 */
void SSD1306_ShowString(uint8_t x, uint8_t y, const char *str, FontSize_t size);

/**
 * @brief 显示中文字符
 * @param x X坐标
 * @param y Y坐标
 * @param chinese 中文字符(UTF-8编码)
 * @param size 字体大小
 */
void SSD1306_ShowChinese(uint8_t x, uint8_t y, const char *chinese, FontSize_t size);

/**
 * @brief 显示数字
 * @param x X坐标
 * @param y Y坐标
 * @param num 数字
 * @param len 显示长度
 * @param size 字体大小
 */
void SSD1306_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, FontSize_t size);

/**
 * @brief 显示浮点数
 * @param x X坐标
 * @param y Y坐标
 * @param num 浮点数
 * @param decimal 小数位数
 * @param size 字体大小
 */
void SSD1306_ShowFloat(uint8_t x, uint8_t y, float num, uint8_t decimal, FontSize_t size);

/**
 * @brief 画线
 * @param x1 起点X坐标
 * @param y1 起点Y坐标
 * @param x2 终点X坐标
 * @param y2 终点Y坐标
 */
void SSD1306_DrawLine(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2);

/**
 * @brief 画矩形
 * @param x X坐标
 * @param y Y坐标
 * @param width 宽度
 * @param height 高度
 * @param fill 是否填充
 */
void SSD1306_DrawRect(uint8_t x, uint8_t y, uint8_t width, uint8_t height, bool fill);

/**
 * @brief 设置显示区域
 * @param x X坐标
 * @param y Y坐标
 * @param width 宽度
 * @param height 高度
 */
void SSD1306_SetDisplayArea(uint8_t x, uint8_t y, uint8_t width, uint8_t height);

/**
 * @brief 检查OLED是否在线
 * @return true 在线, false 离线
 */
bool SSD1306_IsOnline(void);

#endif /* SSD1306_H */
