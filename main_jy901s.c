/*
 * MSPM0G3507 + JY901S + OLED 显示系统主程序
 * 功能: 读取JY901S九轴传感器数据并在OLED屏幕上显示
 * 作者: AI Assistant
 * 日期: 2025-01-29
 */

#include "ti_msp_dl_config.h"
#include "system_config.h"
#include "jy901s.h"
#include "ssd1306.h"
#include "display_manager.h"
#include "sensor_manager.h"
#include <stdio.h>
#include <string.h>

/* 全局变量 */
volatile bool g_sensor_data_ready = false;
volatile bool g_display_update_flag = false;
volatile uint32_t g_system_tick = 0;
SensorData_t g_sensor_data;
SystemStatus_t g_system_status;

/* 任务计数器 */
static uint32_t sensor_task_counter = 0;
static uint32_t display_task_counter = 0;
static uint32_t status_task_counter = 0;

/* 函数声明 */
void System_Init(void);
void Task_Scheduler(void);
void System_Error_Handler(void);

/**
 * @brief 主函数
 */
int main(void)
{
    /* 系统初始化 */
    System_Init();
    
    /* 主循环 */
    while (1)
    {
        /* 任务调度器 */
        Task_Scheduler();
        
        /* 低功耗模式 */
        __WFI();
    }
}

/**
 * @brief 系统初始化
 */
void System_Init(void)
{
    /* 基础系统初始化 */
    SYSCFG_DL_init();
    
    /* 使能中断 */
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
    NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);
    
    /* 硬件驱动初始化 */
    UART_Init();  // JY901S通信
    I2C_Init();   // OLED通信
    
    /* 传感器初始化 */
    JY901S_Init();
    
    /* 显示系统初始化 */
    SSD1306_Init();
    DisplayManager_Init();
    
    /* 传感器管理器初始化 */
    SensorManager_Init();
    
    /* 初始化系统状态 */
    memset(&g_system_status, 0, sizeof(g_system_status));
    g_system_status.sensor_online = true;
    g_system_status.display_online = SSD1306_IsOnline();
    g_system_status.current_page = DISPLAY_PAGE_ANGLE;
    
    /* 启动定时器 */
    DL_Timer_startCounter(TIMER_0_INST);
    
    /* 系统启动指示 */
    DL_GPIO_setPins(GPIO_LEDS_PORT, GPIO_LEDS_USER_LED_1_PIN);
    
    /* 显示启动信息 */
    if (g_system_status.display_online)
    {
        SSD1306_Clear();
        SSD1306_ShowString(20, 10, "JY901S系统", FONT_SIZE_12);
        SSD1306_ShowString(30, 30, "初始化完成", FONT_SIZE_8);
        SSD1306_Update();
        
        /* 延时2秒显示启动信息 */
        for (volatile int i = 0; i < 2000000; i++);
    }
}

/**
 * @brief 任务调度器
 */
void Task_Scheduler(void)
{
    uint32_t current_tick = g_system_tick;
    
    /* 100Hz传感器数据采集任务 */
    if ((current_tick - sensor_task_counter) >= (1000 / TASK_SENSOR_FREQ))
    {
        sensor_task_counter = current_tick;
        
        /* 处理传感器数据 */
        if (g_sensor_data_ready)
        {
            SensorManager_Update();
            g_sensor_data_ready = false;
            g_display_update_flag = true;
            
            /* 更新系统状态 */
            g_system_status.sensor_online = SensorManager_IsDataValid();
        }
    }
    
    /* 20Hz显示更新任务 */
    if ((current_tick - display_task_counter) >= (1000 / TASK_DISPLAY_FREQ))
    {
        display_task_counter = current_tick;
        
        if (g_display_update_flag && g_system_status.display_online)
        {
            DisplayManager_Update();
            g_display_update_flag = false;
        }
    }
    
    /* 1Hz系统状态检查任务 */
    if ((current_tick - status_task_counter) >= (1000 / TASK_STATUS_FREQ))
    {
        status_task_counter = current_tick;
        
        /* LED状态指示 */
        if (g_system_status.sensor_online && g_system_status.display_online)
        {
            /* 系统正常 - LED慢闪 */
            DL_GPIO_togglePins(GPIO_LEDS_PORT, GPIO_LEDS_USER_LED_1_PIN);
        }
        else
        {
            /* 系统异常 - LED常亮 */
            DL_GPIO_setPins(GPIO_LEDS_PORT, GPIO_LEDS_USER_LED_1_PIN);
        }
        
        /* 系统健康检查 */
        if (!JY901S_IsHealthy())
        {
            g_system_status.sensor_error_count++;
            if (g_system_status.display_online)
            {
                DisplayManager_ShowError("传感器异常");
            }
        }
        
        if (!SSD1306_IsOnline())
        {
            g_system_status.display_error_count++;
            g_system_status.display_online = false;
        }
    }
}

/**
 * @brief UART中断处理函数
 */
void UART_0_INST_IRQHandler(void)
{
    switch (DL_UART_getPendingInterrupt(UART_0_INST))
    {
        case DL_UART_IIDX_RX:
            /* 接收JY901S数据 */
            JY901S_UART_IRQHandler();
            g_sensor_data_ready = true;
            break;
        default:
            break;
    }
}

/**
 * @brief 定时器中断处理函数 - 1ms系统时钟
 */
void TIMER_0_INST_IRQHandler(void)
{
    switch (DL_Timer_getPendingInterrupt(TIMER_0_INST))
    {
        case DL_TIMER_IIDX_ZERO:
            g_system_tick++;
            break;
        default:
            break;
    }
}

/**
 * @brief 系统错误处理
 */
void System_Error_Handler(void)
{
    /* 关闭所有中断 */
    __disable_irq();
    
    /* 错误指示 */
    DL_GPIO_setPins(GPIO_LEDS_PORT, GPIO_LEDS_USER_LED_1_PIN);
    
    /* 显示错误信息 */
    if (g_system_status.display_online)
    {
        SSD1306_Clear();
        SSD1306_ShowString(30, 10, "系统错误", FONT_SIZE_12);
        SSD1306_ShowString(20, 30, "请重启设备", FONT_SIZE_8);
        SSD1306_Update();
    }
    
    /* 死循环 */
    while (1)
    {
        /* 错误LED快速闪烁 */
        DL_GPIO_togglePins(GPIO_LEDS_PORT, GPIO_LEDS_USER_LED_1_PIN);
        for (volatile int i = 0; i < 100000; i++); // 短延时
    }
}

/**
 * @brief 断言失败处理
 */
void __assert_func(const char *file, int line, const char *func, const char *expr)
{
    /* 断言失败时调用错误处理 */
    System_Error_Handler();
}

/**
 * @brief NMI中断处理
 */
void NMI_Handler(void)
{
    System_Error_Handler();
}

/**
 * @brief 硬件错误中断处理
 */
void HardFault_Handler(void)
{
    System_Error_Handler();
}
