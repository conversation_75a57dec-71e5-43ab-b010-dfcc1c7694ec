# MSPM0G3507 + JY901S + OLED 显示系统

## 项目简介

这是一个基于TI MSPM0G3507开发板的九轴传感器数据显示系统，通过UART接口读取JY901S传感器数据，并在SSD1306 OLED屏幕上实时显示姿态信息。

## 硬件配置

### 主控制器
- **MCU**: TI MSPM0G3507 (80MHz ARM Cortex-M0+)
- **Flash**: 128KB
- **RAM**: 32KB

### 传感器模块
- **型号**: JY901S 九轴姿态传感器
- **通信**: UART (9600bps)
- **数据**: 加速度、角速度、角度、磁场、温度

### 显示模块
- **型号**: SSD1306 OLED显示屏
- **分辨率**: 128x64像素
- **通信**: I2C (400kHz)
- **地址**: 0x3C

## 引脚连接

### JY901S传感器连接
```
JY901S    MSPM0G3507
VCC   ->  3.3V
GND   ->  GND
TX    ->  PA11 (UART0_RX)
RX    ->  PA10 (UART0_TX)
```

### OLED显示屏连接
```
OLED      MSPM0G3507
VCC   ->  3.3V
GND   ->  GND
SDA   ->  PA0 (I2C0_SDA)
SCL   ->  PA1 (I2C0_SCL)
```

### 状态指示LED
```
LED   ->  PA26
```

## 软件架构

### 分层设计
```
应用层
├── main_jy901s.c          # 主程序和任务调度
├── display_manager.c      # 显示管理
└── sensor_manager.c       # 传感器数据管理

驱动层
├── jy901s.c              # JY901S传感器驱动
├── ssd1306.c             # OLED显示驱动
└── ti_msp_dl_config.c    # 硬件配置

配置层
├── system_config.h       # 系统配置
├── font.h               # 字库文件
└── ti_msp_dl_config.h   # TI驱动库配置
```

### 任务调度
- **100Hz**: 传感器数据采集和处理
- **20Hz**: OLED显示更新
- **1Hz**: 系统状态检查和LED指示

## 功能特性

### 数据显示
1. **姿态角度页面**
   - 横滚角 (Roll)
   - 俯仰角 (Pitch)  
   - 偏航角 (Yaw)

2. **加速度页面**
   - X轴加速度
   - Y轴加速度
   - Z轴加速度

3. **角速度页面**
   - X轴角速度
   - Y轴角速度
   - Z轴角速度

### 系统特性
- **自动页面切换**: 每3秒自动切换显示页面
- **数据滤波**: 低通滤波减少噪声
- **错误处理**: 传感器异常检测和显示
- **状态指示**: LED指示系统运行状态
- **中文界面**: 支持中文标签显示

## 编译和使用

### 开发环境
- **IDE**: Code Composer Studio (CCS)
- **SDK**: MSPM0-SDK
- **编译器**: TI ARM Clang

### 编译步骤
1. 安装CCS和MSPM0-SDK
2. 创建新的MSPM0G3507项目
3. 将所有源文件添加到项目中
4. 配置包含路径和库文件
5. 编译并下载到开发板

### 使用说明
1. 连接硬件按照引脚定义
2. 上电后系统自动初始化
3. OLED显示启动信息
4. 开始实时显示传感器数据
5. 页面每3秒自动切换

## 性能指标

### 数据更新
- **传感器采样率**: 100Hz
- **显示刷新率**: 20Hz
- **数据延迟**: <10ms

### 精度指标
- **角度精度**: ±0.1°
- **加速度精度**: ±0.01g
- **角速度精度**: ±1°/s

### 系统资源
- **内存使用**: <8KB RAM
- **Flash使用**: <64KB
- **CPU占用**: <50%

## 故障排除

### 常见问题
1. **OLED无显示**
   - 检查I2C连接
   - 确认电源供电
   - 验证I2C地址

2. **传感器无数据**
   - 检查UART连接
   - 确认波特率设置
   - 检查传感器供电

3. **数据异常**
   - 检查传感器校准
   - 验证数据格式
   - 检查滤波参数

### 调试方法
- 使用CCS调试器
- 监控UART通信
- 检查I2C时序
- 观察LED状态指示

## 扩展功能

### 可选增强
- 添加按键控制页面切换
- 实现数据记录功能
- 增加蓝牙/WiFi通信
- 支持更多传感器类型
- 添加图形化显示

### 移植说明
- 可移植到其他MSPM0系列MCU
- 支持不同尺寸OLED屏幕
- 兼容其他九轴传感器
- 可适配不同通信接口

## 版本信息

- **版本**: v1.0
- **日期**: 2025-01-29
- **作者**: AI Assistant
- **许可**: MIT License

## 技术支持

如有问题请参考：
- TI MSPM0官方文档
- JY901S用户手册
- SSD1306数据手册
- 项目源码注释
